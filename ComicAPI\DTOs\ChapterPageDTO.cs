using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using ComicAPI.Data;
using ComicAPI.Models;

namespace ComicAPI.DTOs;

public class ChapterPageDTO : ChapterDTO
{
    public ChapterPageDTO(Chapter chapter) : base(chapter)
    {

    }
    public List<string>? Pages { get; set; }
    public ComicDTO? Comic { get; set; }
    public List<ChapterServerDTO>? ChapterServers { get; set; }
    // public List<ChapterServerDTO>? ChapterServers { get; set; }
}

