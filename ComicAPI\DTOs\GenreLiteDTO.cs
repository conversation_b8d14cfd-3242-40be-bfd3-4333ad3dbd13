using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using ComicAPI.Data;
using ComicAPI.Models;

namespace ComicAPI.DTOs;

public class GenreLiteDTO
{
    public GenreLiteDTO() { }
    public GenreLiteDTO(Genre genre)
    {
        ID = genre.ID;
        Title = genre.Title;
        Slug = genre.Slug;
    }
    public int ID { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Slug { get; set; } = string.Empty;

}
