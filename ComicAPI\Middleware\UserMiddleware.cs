
using System.Security.Claims;
using ComicAPI.Models;
using ComicAPI.Reposibility;
using ComicAPI.Services;
namespace ComicAPI.Middleware;

public class UserMiddleware
{
    private readonly RequestDelegate _next;

    public UserMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task InvokeAsync(HttpContext context, IUserService userService, IUserReposibility userReposibility, BatchUpdateService _batchUpdateService)
    {
        if (context.User?.Identity?.IsAuthenticated ?? false)
        {
            if (userService != null)
            {
                var strId = context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                User? user = await userReposibility.GetUser(int.Parse(strId!));
                if (user != null)
                {
                    userService.CurrentUser = user;
                    _batchUpdateService.TraceUserLogin(user.ID);
                }
                else
                {
                    context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                    await context.Response.WriteAsync("Unauthorized: Invalid user.");
                    return;
                }
            }

        }
        await _next(context);
    }


}
