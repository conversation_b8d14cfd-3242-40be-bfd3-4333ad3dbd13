using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ComicAPI.Models
{
    public class Chapter
    {
        [Column("id")]
        public int ID { get; set; } // Primary Key (implicitly set by IDENTITY(1, 1))

        [Column("comicid")]
        public int ComicID { get; set; } // Foreign Key

        [Column("title")]
        public string Title { get; set; } = string.Empty;
        [Column("url")]
        public float Url { get; set; } = 0;

        [Column("viewcount")]
        public int ViewCount { get; set; }
        [Column("updateat")]
        public DateTime UpdateAt { get; set; }

        [Column("pages")]
        public string? Pages { get; set; }

        public List<ChapterServer> ChapterServers { get; set; } = new List<ChapterServer>();
        public Comic? Comic { get; internal set; }

        // public Comic? Comic { get; set; } // Navigation property for the related Comic entity

    }

}

