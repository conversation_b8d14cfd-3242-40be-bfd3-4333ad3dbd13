using System.Collections.Concurrent;
using ComicAPI.Reposibility;


public class BatchUpdateService
{
    private readonly ConcurrentDictionary<int, (bool isLogin, int exp)> _userDatas = new(); // user id, isLogin, exp
    private readonly ConcurrentDictionary<int, int> chapterViews = new(); // chapter id, view count
    private readonly ConcurrentDictionary<int, int> comicViews = new(); // comic id, view count
    private readonly SemaphoreSlim _userLock = new(1, 1);
    private readonly SemaphoreSlim _comicLock = new(1, 1);
    private DateTime _lastUserFlushTime = DateTime.UtcNow;
    private DateTime _lastComicFlushTime = DateTime.UtcNow;
    private readonly int _batchSize = 1000;
    private readonly TimeSpan _flushInterval = TimeSpan.FromMinutes(10);
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly ILogger<BatchUpdateService> _logger;

    public BatchUpdateService(IServiceScopeFactory scopeFactory, ILogger<BatchUpdateService> logger)
    {
        _logger = logger;
        _scopeFactory = scopeFactory;
    }

    public void TraceUserLogin(int userId)
    {
        if (_userDatas.TryAdd(userId, (true, 0)))
        {
            _ = TryFlushUserAsync(); // fire & forget
        }
    }
    public void AddUserExp(int userId, int exp)
    {
        _userDatas.AddOrUpdate(key: userId, (true, exp), (key, value) => (value.isLogin, value.exp + exp));
        _ = TryFlushUserAsync(); // fire & forget
    }
    public void AddComicView(int chapterid, int comicid)
    {
        chapterViews.AddOrUpdate(chapterid, 1, (key, value) => value + 1);
        comicViews.AddOrUpdate(comicid, 1, (key, value) => value + 1);
        _ = TryFlushComicAsync(); // fire & forget
    }

    private async Task TryFlushUserAsync(bool force = false)
    {
        if (_userDatas.Count == 0) return; // nothing to flush, exit early
        if (!force)
        {
            if (_userDatas.Count < _batchSize && DateTime.UtcNow - _lastUserFlushTime < _flushInterval) return; // not enough data, wait for more data or time to flush
        }
        if (!_userLock.Wait(0)) return; // avoid concurrent access to _userDatas
        try
        {
            var batch = _userDatas.ToList();
            if (batch.Count == 0) return;

            _lastUserFlushTime = DateTime.UtcNow;

            using var scope = _scopeFactory.CreateScope();
            var _userReposibility = scope.ServiceProvider.GetRequiredService<IUserReposibility>();
            var userIds = batch.Where(x => x.Value.isLogin).Select(x => x.Key).ToArray();
            if (userIds.Length > 0)
            {
                if (!await _userReposibility.UpdateLastLogin(userIds))
                {
                    _logger.LogError("Failed to update last login for users: {UserIds}", string.Join(", ", userIds));
                }

            }
            var exps = batch.Where(x => x.Value.exp > 0)?.ToDictionary(x => x.Key, x => x.Value.exp);
            if (exps?.Count > 0)
            {
                if (!await _userReposibility.SyncUserExp(exps))
                {
                    _logger.LogError("Failed to sync user exp for users: {UserIds}", string.Join(", ", exps.Keys));
                }
            }
            _userDatas.Clear();

        }
        finally
        {
            _userLock.Release();
        }
    }
    private async Task TryFlushComicAsync(bool force = false)
    {
        if (chapterViews.Count == 0 && comicViews.Count == 0) return; // nothing to flush, exit early
        if (!force)
        {
            if ((chapterViews.Count + comicViews.Count) < _batchSize && DateTime.UtcNow - _lastComicFlushTime < _flushInterval) return; // not enough data, wait for more data or time to flush
        }
        if (!_comicLock.Wait(0)) return; // avoid concurrent access to _userDatas
        try
        {
            var chapterBatch = chapterViews.ToList();
            var comicBatch = comicViews.ToList();
            if (chapterBatch.Count == 0 && comicBatch.Count == 0) return;


            _lastComicFlushTime = DateTime.UtcNow;

            using var scope = _scopeFactory.CreateScope();
            var _comicReposibility = scope.ServiceProvider.GetRequiredService<IComicReposibility>();
            if (chapterBatch.Count > 0)
            {
                if (await _comicReposibility.SyncViewChapter(chapterBatch.ToDictionary()))
                {
                    chapterViews.Clear();
                }
            }
            if (comicBatch.Count > 0)
            {
                if (await _comicReposibility.SyncViewComic(comicBatch.ToDictionary()))
                {
                    comicViews.Clear();
                }
            }

        }
        finally
        {
            _comicLock.Release();
        }
    }

    public async Task ForceFlushAllAsync()
    {
        await TryFlushUserAsync(true);
        await TryFlushComicAsync(true);
    }

}

