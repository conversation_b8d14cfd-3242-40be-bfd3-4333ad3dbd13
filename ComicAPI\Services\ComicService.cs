
using ComicAPI.Models;
using ComicAPI.Classes;
using System.Text.Json;
using System.Collections.Concurrent;
using Microsoft.Extensions.Options;
using Microsoft.AspNetCore.WebUtilities;
using ComicAPI.Enums;
using ComicAPI.DTOs;
using ComicAPI.Reposibility;

namespace ComicAPI.Services;

public class ComicService : IComicService
{
    private readonly HttpClient _httpClient;
    private readonly IComicReposibility _comicReposibility;
    private readonly BatchUpdateService _batchUpdateService;

    private static readonly List<int> genreWeight = new List<int>();
    private readonly IUserService _userService;
    private readonly UrlService _urlService;
    private readonly AppSetting _config;

    static ComicService()
    {
        for (int i = 0; i < 55; i++)
        {
            genreWeight.Add(1);
        }
        // var data = GlobalConfig.GetString("genresWeight");
        // Console.WriteLine(data);
    }

    public static string AddQueryParam(string url, string key, string value)
    {
        return QueryHelpers.AddQueryString(url, key, value);
    }

    public ComicService(
        BatchUpdateService batchUpdateService,
        IComicReposibility comicReposibility,
        UrlService urlService,
        IUserService userService,
        IOptions<AppSetting> options,
        HttpClient httpClient)
    {
        _comicReposibility = comicReposibility;
        _userService = userService;
        _config = options.Value;
        _urlService = urlService;
        _httpClient = httpClient;
        _batchUpdateService = batchUpdateService;
    }



    public async Task<ServiceResponse<ComicDTO>> GetComic(string key, int maxchapter = -1)
    {
        var data = await _comicReposibility.GetComic(key);
        if (data == null) return ServiceUtilily.GetDataRes<ComicDTO>(null);
        var comicDTO = new ComicDTO(data);
        if (data != null)
        {
            if (maxchapter > 0)
            {
                comicDTO.Chapters = await _comicReposibility.GetChapters(data.ID, 1, Math.Min(maxchapter, 100));
            }
            comicDTO.IsFollow = _userService.CurrentUser != null && await _userService.IsFollowComic(data.ID);
        }
        return ServiceUtilily.GetDataRes<ComicDTO>(comicDTO);
    }



    public async Task<ServiceResponse<List<ComicDTO>>> SearchComicByKeyword(string keyword)
    {
        var result = await _comicReposibility.GetComicByKeyword(keyword);
        return ServiceUtilily.GetDataRes<List<ComicDTO>>(result);

    }

    public async Task<ServiceResponse<List<ComicDTO>>> GetComicsByAuthor(string? author, int size = 20)
    {
        if (string.IsNullOrEmpty(author)) return ServiceUtilily.GetDataRes<List<ComicDTO>>(null);
        var data = await _comicReposibility.GetComicsByAuthor(author, size);
        if (data == null) return ServiceUtilily.GetDataRes<List<ComicDTO>>(null);
        return ServiceUtilily.GetDataRes<List<ComicDTO>>(data);
    }

    public async Task<ServiceResponse<ListComicDTO>> GetComics(ComicQueryParams comicQueryParams)
    {
        int page = comicQueryParams.page < 1 ? 1 : comicQueryParams.page;
        int step = comicQueryParams.step < 1 ? 10 : comicQueryParams.step;
        var data = await _comicReposibility.GetComics(page, step, comicQueryParams.genre, comicQueryParams.status, comicQueryParams.sort);

        return ServiceUtilily.GetDataRes<ListComicDTO>(data);
    }
    public async Task<ServiceResponse<ListComicDTO>> GetHotComics(int page = 1, int step = 30)
    {
        page = page < 1 ? 1 : page;
        step = step < 1 ? 30 : step;
        var data = await _comicReposibility.GetHotComics(page, step);
        return ServiceUtilily.GetDataRes<ListComicDTO>(data);
    }

    public async Task<ServiceResponse<Comic>> AddComic(Comic comic)
    {
        // _dbContext.Comics.Add(comic);
        // await _dbContext.SaveChangesAsync();
        await Task.Delay(1000);
        return ServiceUtilily.GetDataRes<Comic>(null);
    }

    public async Task<ServiceResponse<List<Genre>>> GetGenres()
    {
        // var data = await _dbContext.Genres.ToListAsync();
        await Task.Delay(1000);
        return ServiceUtilily.GetDataRes<List<Genre>>(null);
    }

    public async Task<ServiceResponse<ChapterPageDTO>> GetPagesInChapter(string comic_url, string chapter_key)
    {
        Chapter? chapter = null;

        if (chapter_key.StartsWith("chuong-"))
        {
            string ss = chapter_key.Replace("chuong-", "");
            if (!float.TryParse(ss, out float chapter_slug))
            {
                return ServiceUtilily.GetDataRes<ChapterPageDTO>(null);
            }
            chapter = await _comicReposibility.GetChapter(comic_url, chapter_slug);
        }
        else
        {
            if (!int.TryParse(chapter_key, out int chapter_slug))
            {
                return ServiceUtilily.GetDataRes<ChapterPageDTO>(null);
            }
            chapter = await _comicReposibility.GetChapter(chapter_slug);
        }
        return GetPagesInChapter(chapter);

    }
    // public async Task<ServiceResponse<ChapterPageDTO>> GetPagesInChapter(int chapter_id)
    // {
    //     Chapter? chapter = await _comicReposibility.GetChapter(chapter_id);
    //     return await GetPagesInChapter(chapter);
    // }
    private ServiceResponse<ChapterPageDTO> GetPagesInChapter(Chapter? chapter)
    {
        if (chapter == null || chapter.Comic == null)
            return ServiceUtilily.GetDataRes<ChapterPageDTO>(null);

        // Truy vấn chapter servers (sau khi có chapter để đảm bảo không lỗi context)
        var servers = chapter.ChapterServers
        .OrderByDescending(x =>
        {
            return x.Host == "nettruyen" ? 0 : 1000000 + x.Images?.Length ?? 0;
        })
        .Select(x => new ChapterServerDTO(x))
        .ToList();

        // Xử lý dữ liệu
        ComicDTO comicDTO = new ComicDTO(chapter.Comic);
        List<string>? urlsData = null;

        if (chapter.Pages != null && chapter.Pages != "")
        {
            urlsData = ProcessPages(chapter.Pages, comicDTO.Url, chapter.Url.ToString(), "nettruyenvio.com");
            if (urlsData != null)
            {
                var server = new ChapterServerDTO()
                {
                    ID = 0,
                    Images = urlsData.ToArray()
                };
                if (servers.Count == 0)
                    servers.Add(server);
                else
                    servers.Insert(1, server);
            }

        }

        for (int i = 0; i < servers.Count; i++)
        {

            if (i >= 2)
            {
                servers[i].Images = null;
            }
            else if (servers[i].Host == "nettruyen")
            {
                servers[i].Images = ProcessPages(servers[i].Images, comicDTO.Url, chapter.Url.ToString(), "nettruyenvio.com");
            }
            servers[i].Host = null;
        }

        return ServiceUtilily.GetDataRes<ChapterPageDTO>(
                new ChapterPageDTO(chapter)
                {
                    Pages = [],
                    Comic = comicDTO,
                    ChapterServers = servers,
                    // ChapterServers = servers.Select(s => new ChapterServerDTO(s)).ToList()
                }
            );
    }


    public async Task<ServiceResponse<ChapterServerDTO>> GetChapterServer(int serverId)
    {
        ChapterServer? data = await _comicReposibility.GetChapterServerById(serverId);
        if (data == null) return ServiceUtilily.GetDataRes<ChapterServerDTO>(null);
        return ServiceUtilily.GetDataRes<ChapterServerDTO>(
            new ChapterServerDTO(data)
        );
    }


    private List<string>? ProcessPages(string? pages, string comicUrl, string chapterUrl, string referer)
    {
        if (pages == null) return null;

        List<string>? links = JsonSerializer.Deserialize<List<string>>(pages.Replace("'", "\""));
        return ProcessPages(links?.ToArray(), comicUrl, chapterUrl, referer)?.ToList();
    }
    private string[]? ProcessPages(string[]? links, string comicUrl, string chapterUrl, string referer)
    {
        if (links == null || links.Length == 0) return null;
        bool needEncode = true;
        return links.Select((url, i) =>
        {
            string encodedUrl = AddQueryParam(url, "referer", referer);
            var uri = new Uri(url);
            string path = uri.AbsolutePath;
            string ext = Path.GetExtension(path);
            string newUrl = needEncode
                ? $"{_urlService.ImgHost}/image/{comicUrl}/{chapterUrl}/{i + 1}{ext}?data={ServiceUtilily.Base64Encode(encodedUrl)}"
                : encodedUrl;

            return newUrl;
        }).ToArray();
    }

    public async Task<ServiceResponse<List<ChapterDTO>>> GetChapters(int comicid)
    {
        var data = await _comicReposibility.GetChapters(comicid);
        return ServiceUtilily.GetDataRes<List<ChapterDTO>>(data);

    }

    // public async Task<ServiceResponse<List<ComicDTO>>> GetSimilarComics(string key)
    // {

    //     ServiceResponse<List<ComicDTO>>? response = await SearchComicByKeyword(key);
    //     foreach (var item in response.Data!)
    //     {
    //         item.Chapters = await _comicReposibility.GetChapters(item.ID.ToString()) ?? [];
    //     }

    //     return response;
    // }
    public async Task<ServiceResponse<List<ComicDTO>>> FindSimilarComics(int id)
    {

        var resultDTO = await _comicReposibility.FindSimilarComics(id);
        return ServiceUtilily.GetDataRes<List<ComicDTO>>(resultDTO);

    }
    private List<int>? ParseGenreQuery(string? query)
    {
        if (string.IsNullOrEmpty(query)) return null;
        List<int> querys = new List<int>();
        string[] genreStrings = query.Split(',');
        foreach (string genreString in genreStrings)
        {
            if (int.TryParse(genreString, out int genre))
            {
                querys.Add((int)genre);
            }
        }
        return querys;
    }
    public async Task<ServiceResponse<ListComicDTO>> GetComicBySearchAdvance(ComicQuerySearchAdvance query)
    {
        int page = Math.Max(1, query.Page);
        int step = Math.Max(1, query.Step);
        var Genres = ParseGenreQuery(query.Genres);
        var Notgenres = ParseGenreQuery(query.Notgenres);
        var data = await _comicReposibility.GetComicBySearchAdvance(query.Sort, query.Status, Genres, page, step, Notgenres, query.Year, query.Keyword);
        return ServiceUtilily.GetDataRes<ListComicDTO>(data);

    }

    public async Task<ServiceResponse<List<ComicDTO>>> GetComicRecommend()
    {
        var data = await _comicReposibility.GetComicRecommend();
        return ServiceUtilily.GetDataRes<List<ComicDTO>>(data);
    }

    public async Task<ServiceResponse<List<ComicDTO>?>> GetTopViewComics(TopViewType type, int step)
    {
        var data = await _comicReposibility.GetTopViewComics(type, step);
        return ServiceUtilily.GetDataRes<List<ComicDTO>>(data)!;
    }
    public async Task<ServiceResponse<int>> TrackView(int chapterid)
    {
        var chapter = await _comicReposibility.GetChapter(chapterid);
        if (chapter == null) return ServiceUtilily.GetDataRes<int>(0);
        _batchUpdateService.AddComicView(chapterid, chapter.ComicID);
        return ServiceUtilily.GetDataRes<int>(chapter.ViewCount + 1);
    }



    public async Task<ServiceResponse<List<ComicDTO>>> GetComicsByIds(List<int> ids)
    {
        List<ComicDTO>? data = await _comicReposibility.GetComicsByIds(ids);
        return ServiceUtilily.GetDataRes<List<ComicDTO>>(data);
    }

    public async Task<ServiceResponse<bool>> ReportError(string name, int chapterid, string errorType, string? message)
    {
        Chapter? chapter = await _comicReposibility.GetChapter(chapterid);
        if (chapter == null) return ServiceUtilily.GetDataRes<bool>(false);
        var data = new
        {
            name = name,
            errorType = errorType,
            chapterLink = $"{_config.Host}/truyen-tranh/chapter/{chapter.ID}",
            createdAt = DateTime.UtcNow,
            message = message ?? "",
        };
        HttpResponseMessage? response = await _httpClient.PostAsJsonAsync(_config.ReportApiUrl, data);
        if (response.IsSuccessStatusCode)
        {
            return ServiceUtilily.GetDataRes<bool>(true);
        }
        return ServiceUtilily.GetDataRes<bool>(false);
    }
    public async Task<ServiceResponse<bool>> Feedback(string name, string? content)
    {
        if (content == null) return ServiceUtilily.GetDataRes<bool>(false);
        var data = new
        {
            name = name,
            message = content,
        };
        HttpResponseMessage? response = await _httpClient.PostAsJsonAsync(_config.FeedbackApiUrl, data);
        if (response.IsSuccessStatusCode)
        {
            return ServiceUtilily.GetDataRes<bool>(true);
        }
        return ServiceUtilily.GetDataRes<bool>(false);
    }

    public async Task<ServiceResponse<List<AnnouncementDTO>>> GetAnnouncement()
    {
        var data = await _comicReposibility.GetAnnouncements();
        return ServiceUtilily.GetDataRes<List<AnnouncementDTO>>(data);
    }
}

