
using ComicAPI.Models;
using Microsoft.EntityFrameworkCore;
using ComicAPI.DTOs;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using ComicAPI.Enums;
using System.Collections.Concurrent;
using ComicAPI.Reposibility;
using ComicAPI.Updater;


namespace ComicAPI.Services;

public class UserService : IUserService
{
    private readonly IBackgroundTaskQueue _taskQueue;
    private readonly IUserReposibility _userReposibility;
    private readonly UrlService _urlService;
    private readonly IServiceProvider _serviceProvider;
    private readonly BatchUpdateService _batchUpdateService;
    private readonly HttpClient _httpClient;

    private User? _currentUser;
    public User? CurrentUser
    {
        get
        {
            return _currentUser;
        }
        set
        {
            _currentUser = value;
        }
    }

    public bool HasLogin => _currentUser != null;

    //Contructor
    public UserService(
        BatchUpdateService batchUpdateService,
      IUserReposibility userReposibility, UrlService urlService, IBackgroundTaskQueue taskQueue, IServiceProvider serviceProvider, HttpClient httpClient)

    {
        _urlService = urlService;
        _userReposibility = userReposibility;
        _taskQueue = taskQueue;
        _serviceProvider = serviceProvider;
        _httpClient = httpClient;
        _batchUpdateService = batchUpdateService;

    }

    public async Task<ServiceResponse<int>> FollowComic(int comicid)
    {
        if (!HasLogin) return new ServiceResponse<int> { Status = 404, Message = "Thông tin người dùng không tồn tại", Data = 0 };

        int status = await _userReposibility.FollowComic(_currentUser!.ID, comicid);
        if (status == 0) return new ServiceResponse<int> { Status = 0, Message = "Failed", Data = 0 };
        _taskQueue.QueueBackgroundWorkItem(async cancellationToken =>
        {
            using (var scope = _serviceProvider.CreateScope())
            {
                var questRepository = scope.ServiceProvider.GetRequiredService<IQuestRepository>();
                await questRepository.TrackFavoriteAsync(_currentUser!.ID, comicid);
            }
        });
        return new ServiceResponse<int> { Status = 1, Message = "Theo dõi thành công", Data = 0 };
    }

    public async Task<ServiceResponse<int>> UnFollowComic(int comicid)
    {
        if (!HasLogin) return new ServiceResponse<int> { Status = 404, Message = "Thông tin người dùng không tồn tại", Data = 0 };
        int status = await _userReposibility.UnFollowComic(_currentUser!.ID, comicid);
        if (status == 0) return new ServiceResponse<int> { Status = 0, Message = "Failed", Data = 0 };
        return new ServiceResponse<int> { Status = 1, Message = "Bỏ theo dõi thành công", Data = 0 };
    }

    public async Task<ServiceResponse<ListComicDTO>> GetFollowComics(int page = 1, int step = 40)
    {
        if (!HasLogin) return new ServiceResponse<ListComicDTO> { Status = 404, Message = "Thông tin người dùng không tồn tại", Data = null };
        ListComicDTO? data = await _userReposibility.GetFollowComics(_currentUser!.ID, page, step);
        return ServiceUtilily.GetDataRes(data);
    }

    public async Task<bool> IsFollowComic(int comicid)
    {
        if (!HasLogin) return false;
        return await _userReposibility.IsFollowComic(_currentUser!.ID, comicid);
    }

    public async Task<ServiceResponse<CommentDTO>> AddComment(string content, int chapterid, int? replyFromCmt)
    {
        if (!HasLogin) return new ServiceResponse<CommentDTO> { Status = 404, Message = "Thông tin người dùng không tồn tại", Data = null };

        var cmtData = await _userReposibility.AddComment(_currentUser!, content, chapterid, replyFromCmt);

        if (cmtData == null) return new ServiceResponse<CommentDTO> { Status = 0, Message = "Failed", Data = null };

        _taskQueue.QueueBackgroundWorkItem(async cancellationToken =>
        {
            using (var scope = _serviceProvider.CreateScope())
            {
                var questRepository = scope.ServiceProvider.GetRequiredService<IQuestRepository>();
                await questRepository.TrackCommentAsync(_currentUser!.ID, chapterid);
            }
        });
        return new ServiceResponse<CommentDTO> { Status = 1, Message = "Success", Data = cmtData };
    }

    public async Task<ServiceResponse<CommentPageDTO>> GetCommentsOfComic(int comicid, int page = 1, int step = 10)
    {
        var data = await _userReposibility.GetCommentsOfComic(comicid, page, step);
        return ServiceUtilily.GetDataRes(data);
    }

    public Task<ServiceResponse<CommentPageDTO>> GetCommentsOfChapter(int chapterid, int page = 1, int step = 10)
    {
        throw new NotImplementedException();
    }

    public async Task<ServiceResponse<UserDTO>> UpdateInfo(UpdateUserInfo request)
    {
        var response = new ServiceResponse<UserDTO>();
        if (CurrentUser == null)
        {
            response.Status = 404;
            response.Message = "Thông tin người dùng không tồn tại";
            response.Data = null;
            return response;
        }
        request.UserId = CurrentUser.ID;
        var user = await _userReposibility.UpdateInfo(request);
        if (user == null)
        {
            response.Status = 404;
            response.Message = "Thông tin người dùng không tồn tại";
            response.Data = null;
            return response;
        }
        CurrentUser = user;
        response.Data = new UserDTO(user);
        response.Status = 200;
        response.Message = "Cập nhật thông tin thành công";
        return response;
    }

    public async Task<ServiceResponse<string>> UpdatePassword(UpdateUserPassword request)
    {
        var response = new ServiceResponse<string>();
        if (CurrentUser == null)
        {
            response.Status = 404;
            response.Message = "Thông tin người dùng không tồn tại";
            return response;
        }
        else if (CurrentUser.HashPassword != request.OldPassword)
        {
            response.Status = 404;
            response.Message = "Password is incorrect";
            return response;
        }
        else if (request.NewPassword != request.RePassword)
        {
            response.Status = 404;
            response.Message = "Confirm password is incorrect";
            return response;
        }
        request.UserId = CurrentUser.ID;
        await _userReposibility.UpdatePassword(request);
        response.Status = 200;
        response.Message = "Success";

        return response;
    }
    public async Task<ServiceResponse<string>> UpdateMaxim(string? maxim)
    {
        if (CurrentUser == null)
        {
            return new ServiceResponse<string> { Status = 404, Message = "Thông tin người dùng không tồn tại" };
        }
        var user = await _userReposibility.UpdateMaxim(CurrentUser.ID, maxim);
        return new ServiceResponse<string> { Status = 200, Message = "Update success" };
    }
    public async Task<ServiceResponse<string>> UpdateAvatar(IFormFile avatar)
    {
        var response = new ServiceResponse<string>();

        try
        {
            if (CurrentUser == null)
            {
                response.Status = 404;
                response.Message = "Thông tin người dùng không tồn tại";
                return response;
            }

            if (avatar == null || avatar.Length == 0)
            {
                return new ServiceResponse<string>
                {
                    Status = 400,
                    Message = "Invalid avatar file"
                };
            }

            var validImageTypes = new[] { "image/jpeg", "image/png", "image/gif", "image/bmp" };
            if (!validImageTypes.Contains(avatar.ContentType))
            {
                response.Status = 400;
                response.Message = "Invalid file type. Only JPEG, PNG, GIF, and BMP are allowed";
                return response;
            }

            if (avatar.Length > 3 * 1024 * 1024)
            {
                response.Status = 400;
                response.Message = "File size exceeds the 3MB limit";
                return response;
            }

            var uploadsFolder = _urlService.GetPathSaveUserImage();
            var fileExtension = Path.GetExtension(avatar.FileName);
            var fileName = $"{CurrentUser.Email}{fileExtension}";
            var filePath = Path.Combine(uploadsFolder, fileName);

            if (!Directory.Exists(uploadsFolder))
            {
                Directory.CreateDirectory(uploadsFolder);
            }

            if (CurrentUser.Avatar != null)
            {
                var oldFilePath = Path.Combine(uploadsFolder, CurrentUser.Avatar);
                if (File.Exists(oldFilePath))
                {
                    File.Delete(oldFilePath);
                }
            }

            using (var memoryStream = new MemoryStream())
            {
                await avatar.CopyToAsync(memoryStream);
                memoryStream.Seek(0, SeekOrigin.Begin);

                using (var image = await Image.LoadAsync(memoryStream))
                {
                    int cropbox = Math.Min(image.Height, image.Width);
                    int cropWidth = Math.Min(cropbox, image.Width);
                    int cropHeight = Math.Min(cropbox, image.Height);
                    int cropX = (image.Width - cropWidth) / 2;
                    int cropY = (image.Height - cropHeight) / 2;

                    var cropRectangle = new Rectangle(cropX, cropY, cropWidth, cropHeight);

                    image.Mutate(x => x.Crop(cropRectangle));
                    image.Mutate(x => x.Resize(200, 200));

                    await using (var fileStream = new FileStream(filePath, FileMode.Create))
                    {
                        switch (avatar.ContentType)
                        {
                            case "image/jpeg":
                                await image.SaveAsJpegAsync(fileStream);
                                break;
                            case "image/png":
                                await image.SaveAsPngAsync(fileStream);
                                break;
                            case "image/gif":
                                await image.SaveAsGifAsync(fileStream);
                                break;
                            case "image/bmp":
                                await image.SaveAsBmpAsync(fileStream);
                                break;
                        }
                    }


                }
            }
            await _userReposibility.UpdateAvatar(CurrentUser.ID, fileName);
            response.Data = _urlService.GetUserImagePath(fileName);
            response.Status = 200;
            response.Message = "Avatar updated successfully";
        }
        catch (Exception ex)
        {
            response.Status = 500;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<string>> UpdateTypelevel(int typelevel)
    {
        if (CurrentUser == null)
        {
            return new ServiceResponse<string> { Status = 404, Message = "Thông tin người dùng không tồn tại" };
        }
        await _userReposibility.UpdateTypelevel(CurrentUser.ID, typelevel);
        return new ServiceResponse<string> { Status = 200, Message = "Success" };
    }

    public async Task<ServiceResponse<UserDTO>> GetMyUserInfo()
    {
        var response = new ServiceResponse<UserDTO>();

        if (!HasLogin)
        {
            response.Status = 404;
            response.Message = "Thông tin người dùng không tồn tại";
            return response;
        }
        response.Status = 200;
        // _CurrentUser.Avatar = _urlService.GetUserImagePath(_CurrentUser.Avatar);
        response.Data = new UserDTO(_currentUser!);
        return await Task.FromResult(response);
    }

    public async Task<ServiceResponse<UserDTO>> GetUserInfo(int id)
    {
        var userData = await _userReposibility.GetUser(id);
        if (userData == null)
        {
            return new ServiceResponse<UserDTO> { Status = 404, Message = "Thông tin người dùng không tồn tại", Data = null };
        }

        // userData.Avatar = _urlService.GetUserImagePath(userData.Avatar);
        return new ServiceResponse<UserDTO> { Status = 200, Message = "Success", Data = new UserDTO(userData) };
    }

    public async Task<ServiceResponse<List<UserNotificationDTO>>> GetUserNotify()
    {
        if (!HasLogin) return new ServiceResponse<List<UserNotificationDTO>> { Status = 404, Message = "Thông tin người dùng không tồn tại", Data = null };
        var response = new ServiceResponse<List<UserNotificationDTO>>();
        var notifys = await _userReposibility.GetUserNotify(_currentUser!.ID);
        if (notifys == null)
        {
            response.Status = 404;
            response.Message = "Thông tin người dùng không tồn tại";
            return response;
        }

        response.Status = 200;
        response.Data = notifys;
        return response;
    }
    public async Task<ServiceResponse<string>> UpdateUserNotify(int? idNotify, bool? isRead = null)
    {
        if (!HasLogin) return new ServiceResponse<string> { Status = 404, Message = "Thông tin người dùng không tồn tại" };
        var response = new ServiceResponse<string>();
        if (await _userReposibility.UpdateUserNotify(_currentUser!.ID, idNotify, isRead))
        {
            response.Status = 200;
            response.Message = "Success";
        }
        else
        {
            response.Status = 404;
            response.Message = "Notification not found";
        }
        return response;
    }
    public async Task<ServiceResponse<string>> DeleteUserNotify(int idNotify)
    {
        if (!HasLogin) return new ServiceResponse<string> { Status = 404, Message = "Thông tin người dùng không tồn tại" };

        var response = new ServiceResponse<string>();
        if (await _userReposibility.DeleteUserNotify(_currentUser!.ID, idNotify))
        {
            response.Status = 200;
            response.Message = "Success";

        }
        else
        {
            response.Status = 404;
            response.Message = "Notification not found";
        }
        return response;
    }

    public async Task<ServiceResponse<int>> VoteComic(int comicid, int votePoint)
    {
        if (!HasLogin) return new ServiceResponse<int> { Status = 404, Message = "Thông tin người dùng không tồn tại", Data = 0 };
        bool flag = await _userReposibility.VoteComic(_currentUser!.ID, comicid, votePoint);
        if (!flag) return new ServiceResponse<int> { Status = 0, Message = "Failed", Data = 0 };
        _taskQueue.QueueBackgroundWorkItem(async cancellationToken =>
        {
            using (var scope = _serviceProvider.CreateScope())
            {
                var questRepository = scope.ServiceProvider.GetRequiredService<IQuestRepository>();
                await questRepository.TrackRatingAsync(_currentUser!.ID, comicid);
            }
        });
        return new ServiceResponse<int> { Status = 1, Message = "Success", Data = 1 };
    }
    public async Task<ServiceResponse<int>> UnVoteComic(int comicid)
    {
        if (!HasLogin) return new ServiceResponse<int> { Status = 404, Message = "Thông tin người dùng không tồn tại", Data = 0 };
        if (await _userReposibility.UnVoteComic(_currentUser!.ID, comicid))
        {
            return new ServiceResponse<int> { Status = 1, Message = "Success", Data = 1 };
        }
        return new ServiceResponse<int> { Status = 0, Message = "Failed", Data = 0 };
    }
    public async Task<ServiceResponse<int>> GetUserVote(int comicid)
    {
        if (!HasLogin) return new ServiceResponse<int> { Status = 404, Message = "Thông tin người dùng không tồn tại", Data = 0 };
        var user = await _userReposibility.GetUserVoteComic(_currentUser!.ID, comicid);
        var votepoint = user?.VotePoint ?? -1;
        return new ServiceResponse<int> { Status = 1, Message = "Success", Data = votepoint };
    }

    public async Task<ServiceResponse<int>> TrackExp(UserExpType expt = UserExpType.Chapter)
    {
        if (!HasLogin) return await Task.FromResult(ServiceUtilily.GetDataRes<int>(-1));
        _batchUpdateService.AddUserExp(_currentUser!.ID, (int)expt);
        return await Task.FromResult(ServiceUtilily.GetDataRes<int>(1));
    }




    public async Task<ServiceResponse<List<UserLiteDTO>>> GetTopUsersByExperience(int limit)
    {
        var tops = await _userReposibility.GetTopUsersByExperience(limit);
        return ServiceUtilily.GetDataRes(tops);
    }


    public async Task<ServiceResponse<List<ConversationDTO>>> GetMyConversations()
    {
        var conversations = await _userReposibility.GetUserConversations(_currentUser?.ID);
        return ServiceUtilily.GetDataRes(conversations);
    }

    public async Task<ServiceResponse<ConversationDTO>> GetConversation(Guid conversationId)
    {
        if (_currentUser == null)
            return new ServiceResponse<ConversationDTO> { Status = 404, Message = "Thông tin người dùng không tồn tại", Data = null };

        var conversation = await _userReposibility.GetConversation(conversationId, _currentUser.ID);
        return ServiceUtilily.GetDataRes(conversation);
    }

    public async Task<ServiceResponse<MessageDTO>> SendMessage(SendMessageDTO request)
    {
        if (_currentUser == null)
            return new ServiceResponse<MessageDTO> { Status = 401, Message = "Thông tin người dùng không tồn tại", Data = null };

        if (string.IsNullOrWhiteSpace(request.Content))
            return new ServiceResponse<MessageDTO> { Status = 400, Message = "Nội dung tin nhắn không được để trống", Data = null };

        MessageDTO? message = null;

        if (request.ConversationId.HasValue)
        {
            message = await _userReposibility.SendMessage(_currentUser.ID, request.ConversationId.Value, request.Content);
        }

        return ServiceUtilily.GetDataRes(message);
    }

    public async Task<ServiceResponse<MessagePageDTO>> GetMessages(Guid conversationId, int page = 1, int pageSize = 50)
    {
        if (_currentUser == null)
            return new ServiceResponse<MessagePageDTO> { Status = 404, Message = "Thông tin người dùng không tồn tại", Data = null };

        var messages = await _userReposibility.GetMessages(conversationId, _currentUser.ID, page, pageSize);
        return ServiceUtilily.GetDataRes(messages);
    }

    public async Task<ServiceResponse<Message>> GetMessage(Guid messageid)
    {
        Message? message = await _userReposibility.GetMessage(messageid);
        return ServiceUtilily.GetDataRes<Message>(message);
    }
}