
using System.Threading.Channels;
using ComicAPI.Services;

namespace ComicAPI.Updater
{
    public interface IBackgroundTaskQueue
    {
        void QueueBackgroundWorkItem(Func<CancellationToken, Task> workItem);
        Task<Func<CancellationToken, Task>> DequeueAsync(CancellationToken cancellationToken);

        void OnStopping();
    }
    public class BackgroundTaskQueue : IBackgroundTaskQueue
    {

        private readonly Channel<Func<CancellationToken, Task>> _queue =
            Channel.CreateUnbounded<Func<CancellationToken, Task>>();

        public void QueueBackgroundWorkItem(Func<CancellationToken, Task> workItem)
        {
            if (workItem == null) throw new ArgumentNullException(nameof(workItem));
            _queue.Writer.TryWrite(workItem);
        }

        public async Task<Func<CancellationToken, Task>> DequeueAsync(CancellationToken cancellationToken)
        {
            return await _queue.Reader.ReadAsync(cancellationToken);
        }
        public void OnStopping()
        {
            _queue.Writer.Complete();
        }
    }


    public class HostedService : BackgroundService
    {
        private readonly BatchUpdateService _batchUpdateService;
        private readonly IHostApplicationLifetime _appLifetime;

        private ulong tick = 0;
        private readonly IBackgroundTaskQueue _taskQueue;
        private readonly IServiceProvider _serviceProvider;
        private List<XTask> _updaters = new List<XTask>();
        private readonly ILogger<HostedService> _logger;

        private readonly TimeSpan _interval;
        public HostedService(IServiceProvider services, IBackgroundTaskQueue taskQueue, ILogger<HostedService> logger, BatchUpdateService batchUpdateService)
        {
            _serviceProvider = services;
            _taskQueue = taskQueue;
            _interval = TimeSpan.FromSeconds(1); // thời gian chạy task định kỳ
            _logger = logger;
            _appLifetime = services.GetRequiredService<IHostApplicationLifetime>();
            _batchUpdateService = batchUpdateService;

        }
        private ulong GetTick()
        {
            return tick + 1;
        }
        public override Task StartAsync(CancellationToken cancellationToken)
        {
            Init();
            _appLifetime.ApplicationStopping.Register(OnStopping);
            base.StartAsync(cancellationToken);
            return Task.CompletedTask;
        }

        private void OnStopping()
        {
            
            // Thực hiện cleanup: đóng DB, flush log, v.v.
            _logger.LogInformation("Unified Background Service is stopping.");


        }
        private void Update(object? state) // Call every 1 second
        {
            tick = GetTick();
            // Console.WriteLine(tick);
            //Implement Task Update Here
            // for (int i = 0; i < _updaters.Count; i++)
            // {
            //     _updaters[i].Update(tick);
            // }
            
        }

        void Init()
        {


        }


        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Unified Background Service is starting.");
            var intervalTask = RunIntervalTaskAsync(stoppingToken);
            var queueTask = ProcessQueueAsync(stoppingToken);

            await Task.WhenAll(intervalTask, queueTask);
            _logger.LogInformation("Unified Background Service is stopping.");
        }

        private async Task RunIntervalTaskAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    this.Update(tick);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "");
                }
                try
                {
                    await Task.Delay(_interval, stoppingToken);

                }
                catch (TaskCanceledException ex)
                {
                    _logger.LogInformation("Task canceled");
                }
            }
        }
        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            await _batchUpdateService.ForceFlushAllAsync();
        }
        private async Task ProcessQueueAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    var workItem = await _taskQueue.DequeueAsync(stoppingToken);
                    await workItem(stoppingToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred executing queued task. " + ex.Message);
                }
            }
        }
    }
}