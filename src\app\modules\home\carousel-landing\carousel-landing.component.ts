import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { CommonModule, NgOptimizedImage } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  computed,
  ElementRef,
  HostListener,
  Inject,
  NgZone,
  OnDestroy,
  OnInit,
  PLATFORM_ID,
  QueryList,
  signal,
  ViewChild,
  ViewChildren
} from '@angular/core';
import { RouterLink } from '@angular/router';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { SpinnerComponent } from '@components/common/spinner/spinner.component';
import { ComicDescriptionPipe } from '@pines/description.pipe';
import { NumeralPipe } from '@pines/numeral.pipe';
import { Comic } from '@schema';
import { ComicService } from '@services/comic.service';
import { UrlService } from '@services/url.service';
import globalConfig from 'globalConfig';
import { interval, Subscription, timer } from 'rxjs';
import { SwiperComponent } from 'src/app/modules/home/<USER>/page/swiper/swiper.component';
import { CarouselLayoutComponent } from './page/carousel-layout/carousel-layout.component';
@Component({
  selector: 'div[app-carousel-landing]',
  templateUrl: './carousel-landing.component.html',
  styleUrl: './carousel-landing.component.scss',
  animations: [
    trigger('formAnimation', [
      state('void', style({ opacity: 0, transform: 'translateY(100%)' })),
      state('*', style({ opacity: 1, transform: 'translateY(0)' })),
      transition('void => *', [animate('200ms ease-out')]),
      transition('* => void', [animate('200ms ease-in')]),
    ]),
  ],
  standalone: true,
  imports: [
    // SwiperComponent,
    NgOptimizedImage,
    CommonModule,
    RouterLink,
    CarouselLayoutComponent,
    SpinnerComponent,
    ComicDescriptionPipe,
    NumeralPipe
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  
})
export class CarouselLandingComponent extends OptimizedBaseComponent implements OnInit, OnDestroy {
  getComicUrl(comic: Comic): any {
    return this.urlService.getComicDetailUrl(comic);
  }
  // Component state using signals for better reactivity
  carouselItems = signal<Comic[][]>([]);
  lastTime = signal<number>(0);
  isTransitioning = signal<boolean>(false);
  hoverComic = signal<Comic | undefined>(undefined);
  _state = signal<string>('out');
  typeUI = signal<number>(0);
  grid = signal<number>(3);
  comicList = signal<Comic[]>([]);
  currentIdx = signal<number>(0);
  attrs = signal<any[]>([]);
  subscription?: Subscription;

  // Performance optimizations
  private readonly AUTO_SLIDE_INTERVAL = 5000;
  private readonly INITIAL_DELAY = 3000;
  private readonly TRANSITION_THROTTLE = 500;
  private readonly DESKTOP_GRID_SIZE = 3;
  private readonly MOBILE_GRID_SIZE_SM = 3;
  private readonly MOBILE_GRID_SIZE_MD = 4;

  private debouncedNext!: Function;
  private debouncedPrev!: Function;

  @ViewChild(SwiperComponent) swiperComponent!: SwiperComponent;
  @ViewChildren("carouselItemPc") carouselElePcs!: QueryList<ElementRef>;
  @ViewChildren("carouselItemMobile") carouselEleMobiles!: QueryList<ElementRef>;
  carouselEles!: QueryList<ElementRef>;

  constructor(
    private cdr: ChangeDetectorRef,
    private ngZone: NgZone,
    @Inject(PLATFORM_ID) override platformId: object,
    private comicService: ComicService,
    private urlService: UrlService
  ) {
    super(cdr, platformId);
    this.setupDebouncedMethods();
  }

  // Computed properties for optimal performance
  hasCarouselItems = computed(() => this.carouselItems().length > 0);
  isDesktop = computed(() => this.typeUI() === 0);
  isMobile = computed(() => this.typeUI() === 1);
  currentCarouselElements = computed(() => 
    this.isDesktop() ? this.carouselElePcs : this.carouselEleMobiles
  );
  shouldShowHoverDetails = computed(() => 
    !!this.hoverComic() && this.isDesktop()
  );


  // TrackBy functions for ngFor optimization
  trackByCarouselIndex = (index: number): number => index;
  trackByComicId = (index: number, comic: Comic): number => comic.id;
  ngOnInit(): void {
    console.log(4345345);
    
    this.ngZone.runOutsideAngular(() => {
      this.runInBrowser(() => {
        this.computeStyleUI();
        this.setupInitialDelay();
      });
    })
    this.loadRecommendedComics();
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
  }

  private setupDebouncedMethods(): void {
    this.debouncedNext = this.debounce(this.performNext.bind(this), 100);
    this.debouncedPrev = this.debounce(this.performPrev.bind(this), 100);
  }

  private setupInitialDelay(): void {
    this.addSubscription(
      timer(this.INITIAL_DELAY).pipe(this.takeUntilDestroy()).subscribe(() => {
        this.resetAutoSlide();
      })
    );
  }

  public getLeftPositon(index: number): string {
    return `${(index - 1) * 100 / this.grid()}%`;
  }

  private loadRecommendedComics(): void {
    this.addSubscription(
      this.comicService.getRecommendComics().pipe(this.takeUntilDestroy()).subscribe((res: any) => {
        if (!res.data) return;
        
        if (this.isServer) {
          const chunks = this.chunk<Comic>(res.data, 5).slice(0,3);
          this.carouselItems.set([...chunks, chunks[0]]);
          this.comicList.set(res.data.slice(0, 4));
        } else {
          const chunks = this.chunk<Comic>(res.data, 5).slice(0, 3);
          this.carouselItems.set([...chunks, ...chunks]);
          this.comicList.set(res.data.slice(0, 10));
        }

        this.safeMarkForCheck();
      })
    );
  }
  chunk<T>(array: T[], size: number): T[][] {
    const result: T[][] = [];

    for (let i = 0; i < array.length; i += size) {
      // Slice the array from the current index to the next 'size' elements
      const chunkedArray = array.slice(i, i + size);
      result.push(chunkedArray); // Add the chunk to the result array
    }

    return result; // Return the array of chunks
  }

  OnComicLeave(): void {
    this._state.set('out');
    this.startAutoSlide();
  }

  private computeStyleUI(): void {
    const windowWidth = window.innerWidth;
    const lgBreakpoint = globalConfig.GetScreenSize('lg');
    const smBreakpoint = globalConfig.GetScreenSize('sm');

    if (windowWidth <= lgBreakpoint) {
      this.typeUI.set(1);
      this.grid.set(windowWidth <= smBreakpoint ? this.MOBILE_GRID_SIZE_SM : this.MOBILE_GRID_SIZE_MD);
    } else {
      this.typeUI.set(0);
      this.grid.set(this.DESKTOP_GRID_SIZE);
    }
  }
  @HostListener('document:visibilitychange', ['$event'])
  onDocumentVisibilityChange(): void {
    this.runInBrowser(() => {
      if (document.hidden) {
        this.stopAutoSlide();
      } else {
        this.startAutoSlide();
      }
    });
  }

  OnComicHover(comic: Comic): void {
    if (this.hoverComic() === comic) return;
    this.stopAutoSlide();
    this.hoverComic.set(comic);
    this.safeMarkForCheck();
  }

  private startAutoSlide(): void {
    this.stopAutoSlide();
    this.ngZone.runOutsideAngular(() => {
      this.subscription = interval(this.AUTO_SLIDE_INTERVAL).pipe(this.takeUntilDestroy()).subscribe(() => this.prev())
    })
    
  }

  private stopAutoSlide(): void {
    this.subscription?.unsubscribe();
  }

  next(step = 1): void {
    this.debouncedNext(step);
  }

  prev(step = 1): void {
    this.debouncedPrev(step);
  }

  private performNext(): void {
    if (!this.canTransition()) return;
    this.updateLastTime();
    this.performRecalculate(true);
    this.swiperComponent?.next();
  }

  private performPrev(): void {
    if (!this.canTransition()) return;
    this.updateLastTime();
    this.performRecalculate(false);
    this.swiperComponent?.prev();
  }

  private canTransition(): boolean {
    const now = Date.now();
    return now - this.lastTime() >= this.TRANSITION_THROTTLE;
  }

  private updateLastTime(): void {
    this.lastTime.set(Date.now());
  }

  private performRecalculate(isNext = true): void {
    this.carouselEles = this.currentCarouselElements();
    if (this.carouselEles.length === 0) return;

    this.runInBrowser(() => {
      this.updateElementVisibility(isNext);
      this.updateCurrentIndex(isNext);
      this.updateElementPositions();
    });
  }

  private updateElementVisibility(isNext: boolean): void {
    this.carouselEles.forEach((e: ElementRef, index) => {
      const value = (index + this.currentIdx()) % this.carouselEles.length;
      e.nativeElement.classList.add('invisible');

      if (isNext && value <= this.grid()) {
        e.nativeElement.classList.remove('invisible');
      } else if (!isNext && value >= 1 && value <= this.grid() + 1) {
        e.nativeElement.classList.remove('invisible');
      }
    });
  }

  private updateCurrentIndex(isNext: boolean): void {
    const direction = isNext ? 1 : -1;
    this.currentIdx.update(current => 
      (current + direction + this.carouselEles.length) % this.carouselEles.length
    );
  }

  private updateElementPositions(): void {
    this.carouselEles.forEach((e: ElementRef, index) => {
      const value = (index + this.currentIdx()) % this.carouselEles.length - 1;
      e.nativeElement.style.left = `${(value * 100) / this.grid()}%`;
      e.nativeElement.style.width = `${100 / this.grid()}%`;
    });
  }

  private resetAutoSlide(): void {
    this.startAutoSlide();
  }
  SlideChange(direct: number): void {
    this.performRecalculate(direct > 0);
  }

}
